import { GoogleAnalyticsProvider } from '@/components/analytics/GoogleAnalyticsProvider'
import { PostHogProvider } from '@/components/analytics/PostHogProvider'
import { ModalManager } from '@/components/modal'
import { Toaster } from '@/components/ui/sonner'
import { routing } from '@/libs/i18nNavigation'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages, setRequestLocale } from 'next-intl/server'
import { notFound } from 'next/navigation'
import '@/styles/global.css'

export default async function RootLayout(props: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await props.params

  if (!routing.locales.includes(locale)) {
    notFound()
  }

  setRequestLocale(locale)

  // Using internationalization in Client Components
  const messages = await getMessages()

  // The `suppressHydrationWarning` attribute in <body> is used to prevent hydration errors caused by Sentry Overlay,
  // which dynamically adds a `style` attribute to the body tag.

  return (
    <html lang={locale}>
      <body suppressHydrationWarning>
        <NextIntlClientProvider
          locale={locale}
          messages={messages}
        >
          <GoogleAnalyticsProvider>
            <PostHogProvider>
              {props.children}
            </PostHogProvider>
            <ModalManager />
          </GoogleAnalyticsProvider>
          <Toaster />
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
