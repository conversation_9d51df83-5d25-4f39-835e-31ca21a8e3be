'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { PAGE_STATUSES, TEMPLATE_NAMES } from '@/modules/admin/constants/mock-data'
import { formatDateYMDHMS } from '@/utils/time'
import { BarChart2, Calendar, Edit, ExternalLink, Eye, MoreVertical, Trash2 } from 'lucide-react'
import Image from 'next/image'
import React from 'react'
import { bookingPageAPIs } from '../apis/booking-page.api'

/**
 * Thông tin cơ bản của một booking page cho UI
 */
export type BookingPageCardItem = {
  _id: string
  name: string
  status: 'active' | 'inactive' | 'pending'
  template: string
  templateCode?: string
  createdAt: string
  description: string
  avatar: string
  views: number
  bookings: number
  slug: string
}

/**
 * Props cho component BookingPageCard
 */
type BookingPageProps = {
  item: BookingPageCardItem
  onEditAction: (id: string) => void
  onViewStatsAction: (id: string) => void
  onViewLiveAction: (id: string, slug: string) => void
  onOpenControlPanel?: (id: string) => void
}

export const BookingPageCard = ({
  item,
  onEditAction,
  onViewStatsAction,
  onViewLiveAction,
  onOpenControlPanel,
}: BookingPageProps) => {
  const {
    _id,
    name,
    status,
    template,
    createdAt,
    description,
    avatar,
    views,
    bookings,
    slug,
  } = item
  const id = _id

  const statusInfo = PAGE_STATUSES[status]
  const templateName = TEMPLATE_NAMES[template as keyof typeof TEMPLATE_NAMES] || template
  const createdDate = formatDateYMDHMS(new Date(createdAt))

  const handleDeleteBookingPage = async (id: string) => {
    try {
      await bookingPageAPIs.deleteBookingPage(id)
    } catch (error) {
      console.error('Error deleting booking page:', error)
    }
  }

  const handleClickPickSlotPage = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    e.preventDefault()
    onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)
  }

  return (
    <>
      <TooltipProvider>
        <Card
          onClick={handleClickPickSlotPage}
          className="overflow-hidden transition-all duration-200 hover:shadow-2xl group bg-white rounded-2xl border border-gray-200 shadow-md p-6 min-h-[260px] cursor-pointer"
        >
          <div className="flex flex-col md:flex-row gap-6 border-b pb-4">
            <button
              type="button"
              className="relative w-24 h-24 rounded-xl overflow-hidden flex-shrink-0 cursor-pointer border-0 p-0 shadow-md"
              onClick={(e) => {
                e.stopPropagation()
                onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)
              }}
              aria-label={`Control ${name}`}
            >
              <Image
                src={avatar}
                alt={name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-110"
              />
            </button>
            <div className="flex-1 min-w-0 flex flex-col gap-2">
              <div className="flex flex-col justify-between gap-2">
                <button
                  type="button"
                  className="text-2xl font-bold truncate cursor-pointer hover:text-primary transition-colors duration-200 text-left border-0 p-0 bg-transparent"
                  onClick={(e) => {
                    e.stopPropagation()
                    onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)
                  }}
                  aria-label={`Control ${name}`}
                >
                  {name}
                </button>
                <div className="text-base text-gray-400">
                  /
                  <span className="font-mono">{slug}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-primary"
                        onClick={(e) => {
                          e.stopPropagation()
                          onViewLiveAction(id, slug)
                        }}
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span className="sr-only">Xem trang thực tế</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Xem trang thực tế</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-primary"
                        onClick={(e) => {
                          e.stopPropagation()
                          onEditAction(id)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Chỉnh sửa</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Chỉnh sửa</p>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-primary"
                        onClick={(e) => {
                          e.stopPropagation()
                          onViewStatsAction(id)
                        }}
                      >
                        <BarChart2 className="h-4 w-4" />
                        <span className="sr-only">Xem thống kê</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Xem thống kê</p>
                    </TooltipContent>
                  </Tooltip>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-500">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Thêm tùy chọn</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteBookingPage(id)
                        }}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Xóa
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 items-center mt-1">
                <Badge
                  variant="outline"
                  className={`bg-${statusInfo.color}-50 text-${statusInfo.color}-700 border-${statusInfo.color}-200 px-3 py-1 text-base rounded-xl`}
                >
                  {statusInfo.label}
                </Badge>
                <span className="text-base text-gray-700 font-medium">{templateName}</span>
              </div>
              <p className="text-base text-gray-600 mt-1 line-clamp-2">{description}</p>
              <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                <Calendar className="h-4 w-4 mr-1" />
                <span>
                  Ngày tạo:
                  {createdDate}
                </span>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-4 flex flex-col md:flex-row justify-between items-center gap-4 text-base rounded-b-2xl mt-2">
            <div className="flex items-center gap-4">
              <div className="flex items-center">
                <Eye className="h-5 w-5 text-gray-500 mr-1" />
                <span>
                  {views}
                  {' '}
                  lượt xem
                </span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-gray-500 mr-1" />
                <span>
                  {bookings}
                  {' '}
                  lượt đặt
                </span>
              </div>
            </div>
            <Button
              size="lg"
              variant="default"
              onClick={(e) => {
                e.stopPropagation()
                onOpenControlPanel ? onOpenControlPanel(id) : onEditAction(id)
              }}
              className="transition-all duration-200 font-bold px-8 py-2 shadow-md hover:scale-105 hover:bg-primary/90"
            >
              Quản lý
            </Button>
          </div>
        </Card>
      </TooltipProvider>
    </>
  )
}
