'use client'

import { Button } from '@/components/ui/button'
import { useNavigation } from '@/hooks/useNavigation'
import { appPaths } from '@/utils/app-routes'
import {
  ArrowLeft,
  ChevronRight,
  GitBranch,
  HelpCircle,
  Home,
  Shield,
  Users,
} from 'lucide-react'
import React from 'react'

interface ProjectSidebarProps {
  isOpen: boolean
}

const ProjectSidebar: React.FC<ProjectSidebarProps> = ({ isOpen }) => {
  const { navigate } = useNavigation()

  const menuItems = [
    {
      icon: Home,
      label: 'Dashboard',
      url: appPaths.admin.dashboard(),
      isActive: true,
    },
    {
      icon: GitBranch,
      label: 'Releases',
      url: '#',
      isActive: false,
    },
  ]

  const projectSettings = [
    {
      icon: Users,
      label: 'Overview',
      url: '#',
      isActive: false,
    },
    {
      icon: Shield,
      label: 'Access Tokens',
      url: '#',
      isActive: false,
    },
    {
      icon: Users,
      label: 'Members',
      url: '#',
      isActive: false,
    },
  ]

  const handleBackToDashboard = () => {
    navigate(appPaths.admin.dashboard())
  }

  return (
    <div className={`bg-white text-gray-800 h-full flex flex-col transition-all duration-300 ${isOpen ? 'w-64' : 'w-16'}`}>
      {/* Back to Dashboard */}
      <div className="p-4 border-b border-gray-700">
        <Button
          onClick={handleBackToDashboard}
          variant="ghost"
          size="sm"
          className="w-full justify-start text-gray-600 hover:text-orange-600 hover:bg-orange-50"
        >
          <div className="absolute -right-2 top-1/2 hidden h-6 w-1 -translate-y-1/2 rounded-l bg-orange-500 group-hover:block" />
          <ArrowLeft className="h-4 w-4" />
          {isOpen && <span className="ml-2">Back to Dashboard</span>}
        </Button>
      </div>

      {/* Projects Section */}
      <div className="p-4">
        <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          {isOpen ? 'Projects' : 'P'}
        </div>
        <div className="space-y-1">
          {menuItems.map(item => (
            <button
              type="button"
              key={item.label}
              className={`w-full flex items-center px-3 py-2 rounded-md text-sm transition-colors ${
                item.isActive
                  ? 'bg-orange-500 text-white'
                  : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
              }`}
              onClick={() => navigate(item.url)}
            >
              <item.icon className="h-4 w-4 flex-shrink-0" />
              {isOpen && (
                <>
                  <span className="ml-3 flex-1 text-left">{item.label}</span>
                  {item.isActive && <ChevronRight className="h-4 w-4" />}
                </>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Project Settings Section */}
      <div className="p-4">
        <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          {isOpen ? 'Project Settings' : 'PS'}
        </div>
        <div className="space-y-1">
          {projectSettings.map(item => (
            <button
              type="button"
              key={item.label}
              className={`w-full flex items-center px-3 py-2 rounded-md text-sm transition-colors ${
                item.isActive
                  ? 'bg-orange-500 text-white'
                  : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
              }`}
              onClick={() => navigate(item.url)}
            >
              <item.icon className="h-4 w-4 flex-shrink-0" />
              {isOpen && (
                <>
                  <span className="ml-3 flex-1 text-left">{item.label}</span>
                  {item.isActive && <ChevronRight className="h-4 w-4" />}
                </>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Support Section */}
      <div className="mt-auto p-4 border-t border-gray-200">
        <button
          type="button"
          className="w-full flex items-center px-3 py-2 rounded-md text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 transition-colors"
        >
          <HelpCircle className="h-4 w-4 flex-shrink-0" />
          {isOpen && <span className="ml-3">Support</span>}
        </button>
      </div>
    </div>
  )
}

export default ProjectSidebar
